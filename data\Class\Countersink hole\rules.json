{"class_name": "Countersink hole", "rules": [{"id": "CS_001", "category": "geometry_validation", "title": "Countersink Angle Validation", "description": "Validate countersink angle based on screw type and application", "rule": "Metric screws: 90°, Imperial screws: 82°, Standard countersunk head: 90°", "severity": "error", "validation_code": "(screw_type == 'metric' && countersink_angle == 90) || (screw_type == 'imperial' && countersink_angle == 82) || (screw_head_type == 'countersunk' && countersink_angle == 90)", "error_message": "ERROR: Invalid countersink angle. Metric screws require 90°, Imperial screws require 82°.", "parameters": ["screw_type", "countersink_angle", "screw_head_type"]}, {"id": "CS_002", "category": "geometry_validation", "title": "Countersink Type Specification", "description": "The countersink type must be clearly specified as through or blind", "rule": "Countersink type must be specified as 'through' or 'blind'", "severity": "error", "validation_code": "countersink_type == 'through' || countersink_type == 'blind'", "error_message": "ERROR: Countersink type must be specified (through or blind).", "parameters": ["countersink_type"]}, {"id": "CS_003", "category": "manufacturing_constraint", "title": "Minimum Material Thickness", "description": "Material thickness must be sufficient for countersink depth with safety margin", "rule": "Material thickness ≥ countersink depth + 0.5mm (minimum safety margin)", "severity": "error", "validation_code": "material_thickness >= (countersink_depth + 0.5)", "error_message": "ERROR: Insufficient material thickness. Required minimum: countersink depth + 0.5mm safety margin.", "parameters": ["material_thickness", "countersink_depth"]}, {"id": "CS_004", "category": "geometry_validation", "title": "Pilot Hole Diameter Validation", "description": "Pilot hole diameter must be appropriate for the screw thread", "rule": "Pilot hole diameter (d1) must match standard clearance values for thread size", "severity": "error", "validation_code": "validatePilotHoleDiameter(thread_size, d1, screw_head_type)", "error_message": "ERROR: Pilot hole diameter d1 does not match standard for the selected thread size and screw type.", "parameters": ["thread_size", "d1", "screw_head_type"]}, {"id": "CS_005", "category": "geometry_validation", "title": "Countersink Diameter Validation", "description": "Countersink diameter must match standard specifications for screw head", "rule": "Countersink diameter (d2) must match standard values for thread size and screw head type", "severity": "error", "validation_code": "validateCountersinkDiameter(thread_size, d2, screw_head_type)", "error_message": "ERROR: Countersink diameter d2 does not match standard for the selected thread size and screw head type.", "parameters": ["thread_size", "d2", "screw_head_type"]}, {"id": "CS_006", "category": "geometry_validation", "title": "Countersink Depth Validation", "description": "Countersink depth must accommodate screw head properly", "rule": "Countersink depth must match or exceed standard screw head height", "severity": "error", "validation_code": "validateCountersinkDepth(thread_size, countersink_depth, screw_head_type, flush_requirement)", "error_message": "ERROR: Countersink depth insufficient for proper screw head seating.", "parameters": ["thread_size", "countersink_depth", "screw_head_type", "flush_requirement"]}, {"id": "CS_007", "category": "geometry_validation", "title": "Concentricity Requirement", "description": "Pilot hole and countersink must be concentric", "rule": "Pilot hole and countersink axes must be concentric within tolerance", "severity": "warning", "validation_code": "concentricity_deviation <= 0.1", "error_message": "WARNING: Pilot hole and countersink concentricity exceeds tolerance (≤0.1mm).", "parameters": ["concentricity_deviation"]}, {"id": "CS_008", "category": "manufacturing_constraint", "title": "Edge Distance Minimum", "description": "Minimum distance from hole center to material edge", "rule": "Distance from hole center to any material edge ≥ 1.5 × countersink diameter", "severity": "warning", "validation_code": "edge_distance >= (1.5 * d2)", "error_message": "WARNING: Insufficient edge distance. Minimum required: 1.5 × countersink diameter.", "parameters": ["edge_distance", "d2"]}, {"id": "CS_009", "category": "manufacturing_constraint", "title": "Tool Access Clearance", "description": "Ensure adequate clearance for countersinking tools", "rule": "Minimum clearance around countersink for tool access", "severity": "warning", "validation_code": "tool_clearance >= 10", "error_message": "WARNING: Insufficient clearance for countersinking tool access (minimum 10mm).", "parameters": ["tool_clearance"]}, {"id": "CS_010", "category": "quality_control", "title": "Surface Finish Requirements", "description": "Countersink surface finish must meet functional requirements", "rule": "Countersink surface roughness Ra ≤ 3.2μm for proper screw seating", "severity": "warning", "validation_code": "surface_roughness <= 3.2", "error_message": "WARNING: Countersink surface roughness exceeds recommended value (Ra ≤ 3.2μm).", "parameters": ["surface_roughness"]}, {"id": "CS_011", "category": "auto_determination", "title": "Standard Dimensions Auto-Determination", "description": "Automatically determine standard dimensions based on thread size and screw type", "rule": "Auto-populate d1, d2, and countersink_depth from standard tables", "severity": "info", "validation_code": "true", "error_message": "INFO: Standard dimensions automatically determined from thread size and screw type.", "parameters": ["thread_size", "screw_head_type"], "auto_determine": {"when": "d1 or d2 or countersink_depth is missing", "logic": {"countersunk_screws": {"M3": {"d1": 3.4, "d2": 6.3, "countersink_depth": 1.7}, "M4": {"d1": 4.5, "d2": 8.6, "countersink_depth": 2.1}, "M5": {"d1": 5.5, "d2": 10.4, "countersink_depth": 2.5}, "M6": {"d1": 6.6, "d2": 12.4, "countersink_depth": 2.9}, "M8": {"d1": 9.0, "d2": 16.4, "countersink_depth": 3.7}, "M10": {"d1": 11.0, "d2": 20.4, "countersink_depth": 4.7}, "M12": {"d1": 13.5, "d2": 24.4, "countersink_depth": 5.2}, "M14": {"d1": 15.5, "d2": 27.4, "countersink_depth": 5.7}, "M16": {"d1": 17.5, "d2": 32.4, "countersink_depth": 7.2}, "M18": {"d1": 20.0, "d2": 36.4, "countersink_depth": 8.2}, "M20": {"d1": 22.0, "d2": 40.4, "countersink_depth": 9.2}}, "socket_head_screws": {"M3": {"d1": 3.4, "d2": 5.5, "countersink_depth": 3.0}, "M4": {"d1": 4.5, "d2": 7.0, "countersink_depth": 4.0}, "M5": {"d1": 5.5, "d2": 8.5, "countersink_depth": 5.0}, "M6": {"d1": 6.6, "d2": 10.0, "countersink_depth": 6.0}, "M8": {"d1": 9.0, "d2": 13.0, "countersink_depth": 8.0}, "M10": {"d1": 11.0, "d2": 16.0, "countersink_depth": 10.0}, "M12": {"d1": 13.5, "d2": 18.0, "countersink_depth": 12.0}}, "button_head_screws": {"M3": {"d1": 3.4, "d2": 5.7, "countersink_depth": 1.65}, "M4": {"d1": 4.5, "d2": 7.6, "countersink_depth": 2.2}, "M5": {"d1": 5.5, "d2": 9.5, "countersink_depth": 2.75}, "M6": {"d1": 6.6, "d2": 10.5, "countersink_depth": 3.3}, "M8": {"d1": 9.0, "d2": 14.0, "countersink_depth": 4.4}, "M10": {"d1": 11.0, "d2": 17.5, "countersink_depth": 5.5}}}}}, {"id": "CS_012", "category": "dimensional_tolerance", "title": "Dimensional Tolerances", "description": "Standard dimensional tolerances for countersink features", "rule": "Pilot hole: H11, Countersink diameter: ±0.1mm, Countersink angle: ±1°", "severity": "info", "validation_code": "validateTolerances(d1_tolerance, d2_tolerance, angle_tolerance)", "error_message": "INFO: Standard tolerances applied - Pilot hole: H11, Countersink: ±0.1mm, Angle: ±1°.", "parameters": ["d1_tolerance", "d2_tolerance", "angle_tolerance"]}, {"id": "CS_013", "category": "manufacturing_constraint", "title": "Blind Hole Penetration Prevention", "description": "Ensures that a blind countersink hole does not drill through the material.", "rule": "If 'countersink_type' is 'blind', then 'countersink_depth' must be strictly less than 'material_thickness'.", "severity": "error", "validation_code": "countersink_type !== 'blind' || (countersink_depth < material_thickness)", "error_message": "ERROR: For blind countersinks, the depth must be less than the material thickness to avoid drilling through.", "parameters": ["countersink_type", "countersink_depth", "material_thickness"]}]}