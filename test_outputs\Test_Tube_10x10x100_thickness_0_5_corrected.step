ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-06-05T14:02:51',(''),(''),
  'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('tube','tube','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#301);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#57,#88,#146,#170,#221,#233,#255,#272,#289));
#17 = ADVANCED_FACE('',(#18),#52,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#30,#38,#46));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(0.,0.,0.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(0.,0.,100.));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(0.,0.,0.));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(0.,0.,1.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#22,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(0.,10.,0.));
#34 = LINE('',#35,#36);
#35 = CARTESIAN_POINT('',(0.,0.,0.));
#36 = VECTOR('',#37,1.);
#37 = DIRECTION('',(-0.,1.,0.));
#38 = ORIENTED_EDGE('',*,*,#39,.T.);
#39 = EDGE_CURVE('',#32,#40,#42,.T.);
#40 = VERTEX_POINT('',#41);
#41 = CARTESIAN_POINT('',(0.,10.,100.));
#42 = LINE('',#43,#44);
#43 = CARTESIAN_POINT('',(0.,10.,0.));
#44 = VECTOR('',#45,1.);
#45 = DIRECTION('',(0.,0.,1.));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#24,#40,#48,.T.);
#48 = LINE('',#49,#50);
#49 = CARTESIAN_POINT('',(0.,0.,100.));
#50 = VECTOR('',#51,1.);
#51 = DIRECTION('',(-0.,1.,0.));
#52 = PLANE('',#53);
#53 = AXIS2_PLACEMENT_3D('',#54,#55,#56);
#54 = CARTESIAN_POINT('',(0.,0.,0.));
#55 = DIRECTION('',(1.,0.,-0.));
#56 = DIRECTION('',(0.,0.,1.));
#57 = ADVANCED_FACE('',(#58),#83,.F.);
#58 = FACE_BOUND('',#59,.F.);
#59 = EDGE_LOOP('',(#60,#68,#69,#77));
#60 = ORIENTED_EDGE('',*,*,#61,.F.);
#61 = EDGE_CURVE('',#22,#62,#64,.T.);
#62 = VERTEX_POINT('',#63);
#63 = CARTESIAN_POINT('',(10.,0.,0.));
#64 = LINE('',#65,#66);
#65 = CARTESIAN_POINT('',(0.,0.,0.));
#66 = VECTOR('',#67,1.);
#67 = DIRECTION('',(1.,0.,-0.));
#68 = ORIENTED_EDGE('',*,*,#21,.T.);
#69 = ORIENTED_EDGE('',*,*,#70,.T.);
#70 = EDGE_CURVE('',#24,#71,#73,.T.);
#71 = VERTEX_POINT('',#72);
#72 = CARTESIAN_POINT('',(10.,0.,100.));
#73 = LINE('',#74,#75);
#74 = CARTESIAN_POINT('',(0.,0.,100.));
#75 = VECTOR('',#76,1.);
#76 = DIRECTION('',(1.,0.,-0.));
#77 = ORIENTED_EDGE('',*,*,#78,.F.);
#78 = EDGE_CURVE('',#62,#71,#79,.T.);
#79 = LINE('',#80,#81);
#80 = CARTESIAN_POINT('',(10.,0.,0.));
#81 = VECTOR('',#82,1.);
#82 = DIRECTION('',(0.,0.,1.));
#83 = PLANE('',#84);
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(0.,0.,0.));
#86 = DIRECTION('',(-0.,1.,0.));
#87 = DIRECTION('',(0.,0.,1.));
#88 = ADVANCED_FACE('',(#89,#107),#141,.T.);
#89 = FACE_BOUND('',#90,.T.);
#90 = EDGE_LOOP('',(#91,#92,#93,#101));
#91 = ORIENTED_EDGE('',*,*,#47,.F.);
#92 = ORIENTED_EDGE('',*,*,#70,.T.);
#93 = ORIENTED_EDGE('',*,*,#94,.T.);
#94 = EDGE_CURVE('',#71,#95,#97,.T.);
#95 = VERTEX_POINT('',#96);
#96 = CARTESIAN_POINT('',(10.,10.,100.));
#97 = LINE('',#98,#99);
#98 = CARTESIAN_POINT('',(10.,0.,100.));
#99 = VECTOR('',#100,1.);
#100 = DIRECTION('',(-0.,1.,0.));
#101 = ORIENTED_EDGE('',*,*,#102,.F.);
#102 = EDGE_CURVE('',#40,#95,#103,.T.);
#103 = LINE('',#104,#105);
#104 = CARTESIAN_POINT('',(0.,10.,100.));
#105 = VECTOR('',#106,1.);
#106 = DIRECTION('',(1.,0.,-0.));
#107 = FACE_BOUND('',#108,.T.);
#108 = EDGE_LOOP('',(#109,#119,#127,#135));
#109 = ORIENTED_EDGE('',*,*,#110,.F.);
#110 = EDGE_CURVE('',#111,#113,#115,.T.);
#111 = VERTEX_POINT('',#112);
#112 = CARTESIAN_POINT('',(0.5,0.5,100.));
#113 = VERTEX_POINT('',#114);
#114 = CARTESIAN_POINT('',(9.5,0.5,100.));
#115 = LINE('',#116,#117);
#116 = CARTESIAN_POINT('',(0.5,0.5,100.));
#117 = VECTOR('',#118,1.);
#118 = DIRECTION('',(1.,0.,-0.));
#119 = ORIENTED_EDGE('',*,*,#120,.T.);
#120 = EDGE_CURVE('',#111,#121,#123,.T.);
#121 = VERTEX_POINT('',#122);
#122 = CARTESIAN_POINT('',(0.5,9.5,100.));
#123 = LINE('',#124,#125);
#124 = CARTESIAN_POINT('',(0.5,0.5,100.));
#125 = VECTOR('',#126,1.);
#126 = DIRECTION('',(-0.,1.,0.));
#127 = ORIENTED_EDGE('',*,*,#128,.T.);
#128 = EDGE_CURVE('',#121,#129,#131,.T.);
#129 = VERTEX_POINT('',#130);
#130 = CARTESIAN_POINT('',(9.5,9.5,100.));
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(0.5,9.5,100.));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(1.,0.,-0.));
#135 = ORIENTED_EDGE('',*,*,#136,.F.);
#136 = EDGE_CURVE('',#113,#129,#137,.T.);
#137 = LINE('',#138,#139);
#138 = CARTESIAN_POINT('',(9.5,0.5,100.));
#139 = VECTOR('',#140,1.);
#140 = DIRECTION('',(-0.,1.,0.));
#141 = PLANE('',#142);
#142 = AXIS2_PLACEMENT_3D('',#143,#144,#145);
#143 = CARTESIAN_POINT('',(0.,0.,100.));
#144 = DIRECTION('',(0.,0.,1.));
#145 = DIRECTION('',(1.,0.,-0.));
#146 = ADVANCED_FACE('',(#147),#165,.T.);
#147 = FACE_BOUND('',#148,.T.);
#148 = EDGE_LOOP('',(#149,#157,#158,#159));
#149 = ORIENTED_EDGE('',*,*,#150,.F.);
#150 = EDGE_CURVE('',#32,#151,#153,.T.);
#151 = VERTEX_POINT('',#152);
#152 = CARTESIAN_POINT('',(10.,10.,0.));
#153 = LINE('',#154,#155);
#154 = CARTESIAN_POINT('',(0.,10.,0.));
#155 = VECTOR('',#156,1.);
#156 = DIRECTION('',(1.,0.,-0.));
#157 = ORIENTED_EDGE('',*,*,#39,.T.);
#158 = ORIENTED_EDGE('',*,*,#102,.T.);
#159 = ORIENTED_EDGE('',*,*,#160,.F.);
#160 = EDGE_CURVE('',#151,#95,#161,.T.);
#161 = LINE('',#162,#163);
#162 = CARTESIAN_POINT('',(10.,10.,0.));
#163 = VECTOR('',#164,1.);
#164 = DIRECTION('',(0.,0.,1.));
#165 = PLANE('',#166);
#166 = AXIS2_PLACEMENT_3D('',#167,#168,#169);
#167 = CARTESIAN_POINT('',(0.,10.,0.));
#168 = DIRECTION('',(-0.,1.,0.));
#169 = DIRECTION('',(0.,0.,1.));
#170 = ADVANCED_FACE('',(#171,#182),#216,.F.);
#171 = FACE_BOUND('',#172,.F.);
#172 = EDGE_LOOP('',(#173,#174,#175,#181));
#173 = ORIENTED_EDGE('',*,*,#31,.F.);
#174 = ORIENTED_EDGE('',*,*,#61,.T.);
#175 = ORIENTED_EDGE('',*,*,#176,.T.);
#176 = EDGE_CURVE('',#62,#151,#177,.T.);
#177 = LINE('',#178,#179);
#178 = CARTESIAN_POINT('',(10.,0.,0.));
#179 = VECTOR('',#180,1.);
#180 = DIRECTION('',(-0.,1.,0.));
#181 = ORIENTED_EDGE('',*,*,#150,.F.);
#182 = FACE_BOUND('',#183,.F.);
#183 = EDGE_LOOP('',(#184,#194,#202,#210));
#184 = ORIENTED_EDGE('',*,*,#185,.F.);
#185 = EDGE_CURVE('',#186,#188,#190,.T.);
#186 = VERTEX_POINT('',#187);
#187 = CARTESIAN_POINT('',(0.5,0.5,0.));
#188 = VERTEX_POINT('',#189);
#189 = CARTESIAN_POINT('',(9.5,0.5,0.));
#190 = LINE('',#191,#192);
#191 = CARTESIAN_POINT('',(0.5,0.5,0.));
#192 = VECTOR('',#193,1.);
#193 = DIRECTION('',(1.,0.,-0.));
#194 = ORIENTED_EDGE('',*,*,#195,.T.);
#195 = EDGE_CURVE('',#186,#196,#198,.T.);
#196 = VERTEX_POINT('',#197);
#197 = CARTESIAN_POINT('',(0.5,9.5,0.));
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(0.5,0.5,0.));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(-0.,1.,0.));
#202 = ORIENTED_EDGE('',*,*,#203,.T.);
#203 = EDGE_CURVE('',#196,#204,#206,.T.);
#204 = VERTEX_POINT('',#205);
#205 = CARTESIAN_POINT('',(9.5,9.5,0.));
#206 = LINE('',#207,#208);
#207 = CARTESIAN_POINT('',(0.5,9.5,0.));
#208 = VECTOR('',#209,1.);
#209 = DIRECTION('',(1.,0.,-0.));
#210 = ORIENTED_EDGE('',*,*,#211,.F.);
#211 = EDGE_CURVE('',#188,#204,#212,.T.);
#212 = LINE('',#213,#214);
#213 = CARTESIAN_POINT('',(9.5,0.5,0.));
#214 = VECTOR('',#215,1.);
#215 = DIRECTION('',(-0.,1.,0.));
#216 = PLANE('',#217);
#217 = AXIS2_PLACEMENT_3D('',#218,#219,#220);
#218 = CARTESIAN_POINT('',(0.,0.,0.));
#219 = DIRECTION('',(0.,0.,1.));
#220 = DIRECTION('',(1.,0.,-0.));
#221 = ADVANCED_FACE('',(#222),#228,.T.);
#222 = FACE_BOUND('',#223,.T.);
#223 = EDGE_LOOP('',(#224,#225,#226,#227));
#224 = ORIENTED_EDGE('',*,*,#78,.F.);
#225 = ORIENTED_EDGE('',*,*,#176,.T.);
#226 = ORIENTED_EDGE('',*,*,#160,.T.);
#227 = ORIENTED_EDGE('',*,*,#94,.F.);
#228 = PLANE('',#229);
#229 = AXIS2_PLACEMENT_3D('',#230,#231,#232);
#230 = CARTESIAN_POINT('',(10.,0.,0.));
#231 = DIRECTION('',(1.,0.,-0.));
#232 = DIRECTION('',(0.,0.,1.));
#233 = ADVANCED_FACE('',(#234),#250,.T.);
#234 = FACE_BOUND('',#235,.T.);
#235 = EDGE_LOOP('',(#236,#237,#243,#244));
#236 = ORIENTED_EDGE('',*,*,#185,.F.);
#237 = ORIENTED_EDGE('',*,*,#238,.T.);
#238 = EDGE_CURVE('',#186,#111,#239,.T.);
#239 = LINE('',#240,#241);
#240 = CARTESIAN_POINT('',(0.5,0.5,0.));
#241 = VECTOR('',#242,1.);
#242 = DIRECTION('',(0.,0.,1.));
#243 = ORIENTED_EDGE('',*,*,#110,.T.);
#244 = ORIENTED_EDGE('',*,*,#245,.F.);
#245 = EDGE_CURVE('',#188,#113,#246,.T.);
#246 = LINE('',#247,#248);
#247 = CARTESIAN_POINT('',(9.5,0.5,0.));
#248 = VECTOR('',#249,1.);
#249 = DIRECTION('',(0.,0.,1.));
#250 = PLANE('',#251);
#251 = AXIS2_PLACEMENT_3D('',#252,#253,#254);
#252 = CARTESIAN_POINT('',(0.5,0.5,0.));
#253 = DIRECTION('',(-0.,1.,0.));
#254 = DIRECTION('',(0.,0.,1.));
#255 = ADVANCED_FACE('',(#256),#267,.F.);
#256 = FACE_BOUND('',#257,.F.);
#257 = EDGE_LOOP('',(#258,#259,#260,#266));
#258 = ORIENTED_EDGE('',*,*,#245,.F.);
#259 = ORIENTED_EDGE('',*,*,#211,.T.);
#260 = ORIENTED_EDGE('',*,*,#261,.T.);
#261 = EDGE_CURVE('',#204,#129,#262,.T.);
#262 = LINE('',#263,#264);
#263 = CARTESIAN_POINT('',(9.5,9.5,0.));
#264 = VECTOR('',#265,1.);
#265 = DIRECTION('',(0.,0.,1.));
#266 = ORIENTED_EDGE('',*,*,#136,.F.);
#267 = PLANE('',#268);
#268 = AXIS2_PLACEMENT_3D('',#269,#270,#271);
#269 = CARTESIAN_POINT('',(9.5,0.5,0.));
#270 = DIRECTION('',(1.,0.,-0.));
#271 = DIRECTION('',(0.,0.,1.));
#272 = ADVANCED_FACE('',(#273),#284,.F.);
#273 = FACE_BOUND('',#274,.F.);
#274 = EDGE_LOOP('',(#275,#276,#282,#283));
#275 = ORIENTED_EDGE('',*,*,#203,.F.);
#276 = ORIENTED_EDGE('',*,*,#277,.T.);
#277 = EDGE_CURVE('',#196,#121,#278,.T.);
#278 = LINE('',#279,#280);
#279 = CARTESIAN_POINT('',(0.5,9.5,0.));
#280 = VECTOR('',#281,1.);
#281 = DIRECTION('',(0.,0.,1.));
#282 = ORIENTED_EDGE('',*,*,#128,.T.);
#283 = ORIENTED_EDGE('',*,*,#261,.F.);
#284 = PLANE('',#285);
#285 = AXIS2_PLACEMENT_3D('',#286,#287,#288);
#286 = CARTESIAN_POINT('',(0.5,9.5,0.));
#287 = DIRECTION('',(-0.,1.,0.));
#288 = DIRECTION('',(0.,0.,1.));
#289 = ADVANCED_FACE('',(#290),#296,.T.);
#290 = FACE_BOUND('',#291,.T.);
#291 = EDGE_LOOP('',(#292,#293,#294,#295));
#292 = ORIENTED_EDGE('',*,*,#238,.F.);
#293 = ORIENTED_EDGE('',*,*,#195,.T.);
#294 = ORIENTED_EDGE('',*,*,#277,.T.);
#295 = ORIENTED_EDGE('',*,*,#120,.F.);
#296 = PLANE('',#297);
#297 = AXIS2_PLACEMENT_3D('',#298,#299,#300);
#298 = CARTESIAN_POINT('',(0.5,0.5,0.));
#299 = DIRECTION('',(1.,0.,-0.));
#300 = DIRECTION('',(0.,0.,1.));
#301 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#305)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#302,#303,#304)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#302 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#303 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#304 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#305 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#302,
  'distance_accuracy_value','confusion accuracy');
#306 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
ENDSEC;
END-ISO-10303-21;
