ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2025-06-05T14:02:51',(''),(''),
  'Open CASCADE STEP processor 7.8','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('tube','tube','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#301);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#91,#122,#146,#170,#187,#218,#242,#266,#283)
  );
#17 = ADVANCED_FACE('',(#18,#52),#86,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#30,#38,#46));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(0.,0.,0.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(0.,0.,10.));
#26 = LINE('',#27,#28);
#27 = CARTESIAN_POINT('',(0.,0.,0.));
#28 = VECTOR('',#29,1.);
#29 = DIRECTION('',(0.,0.,1.));
#30 = ORIENTED_EDGE('',*,*,#31,.T.);
#31 = EDGE_CURVE('',#22,#32,#34,.T.);
#32 = VERTEX_POINT('',#33);
#33 = CARTESIAN_POINT('',(0.,10.,0.));
#34 = LINE('',#35,#36);
#35 = CARTESIAN_POINT('',(0.,0.,0.));
#36 = VECTOR('',#37,1.);
#37 = DIRECTION('',(-0.,1.,0.));
#38 = ORIENTED_EDGE('',*,*,#39,.T.);
#39 = EDGE_CURVE('',#32,#40,#42,.T.);
#40 = VERTEX_POINT('',#41);
#41 = CARTESIAN_POINT('',(0.,10.,10.));
#42 = LINE('',#43,#44);
#43 = CARTESIAN_POINT('',(0.,10.,0.));
#44 = VECTOR('',#45,1.);
#45 = DIRECTION('',(0.,0.,1.));
#46 = ORIENTED_EDGE('',*,*,#47,.F.);
#47 = EDGE_CURVE('',#24,#40,#48,.T.);
#48 = LINE('',#49,#50);
#49 = CARTESIAN_POINT('',(0.,0.,10.));
#50 = VECTOR('',#51,1.);
#51 = DIRECTION('',(-0.,1.,0.));
#52 = FACE_BOUND('',#53,.F.);
#53 = EDGE_LOOP('',(#54,#64,#72,#80));
#54 = ORIENTED_EDGE('',*,*,#55,.F.);
#55 = EDGE_CURVE('',#56,#58,#60,.T.);
#56 = VERTEX_POINT('',#57);
#57 = CARTESIAN_POINT('',(0.,0.5,0.5));
#58 = VERTEX_POINT('',#59);
#59 = CARTESIAN_POINT('',(0.,9.5,0.5));
#60 = LINE('',#61,#62);
#61 = CARTESIAN_POINT('',(0.,0.5,0.5));
#62 = VECTOR('',#63,1.);
#63 = DIRECTION('',(-0.,1.,0.));
#64 = ORIENTED_EDGE('',*,*,#65,.T.);
#65 = EDGE_CURVE('',#56,#66,#68,.T.);
#66 = VERTEX_POINT('',#67);
#67 = CARTESIAN_POINT('',(0.,0.5,9.5));
#68 = LINE('',#69,#70);
#69 = CARTESIAN_POINT('',(0.,0.5,0.5));
#70 = VECTOR('',#71,1.);
#71 = DIRECTION('',(0.,0.,1.));
#72 = ORIENTED_EDGE('',*,*,#73,.T.);
#73 = EDGE_CURVE('',#66,#74,#76,.T.);
#74 = VERTEX_POINT('',#75);
#75 = CARTESIAN_POINT('',(0.,9.5,9.5));
#76 = LINE('',#77,#78);
#77 = CARTESIAN_POINT('',(0.,0.5,9.5));
#78 = VECTOR('',#79,1.);
#79 = DIRECTION('',(-0.,1.,0.));
#80 = ORIENTED_EDGE('',*,*,#81,.F.);
#81 = EDGE_CURVE('',#58,#74,#82,.T.);
#82 = LINE('',#83,#84);
#83 = CARTESIAN_POINT('',(0.,9.5,0.5));
#84 = VECTOR('',#85,1.);
#85 = DIRECTION('',(0.,0.,1.));
#86 = PLANE('',#87);
#87 = AXIS2_PLACEMENT_3D('',#88,#89,#90);
#88 = CARTESIAN_POINT('',(0.,0.,0.));
#89 = DIRECTION('',(1.,0.,-0.));
#90 = DIRECTION('',(0.,0.,1.));
#91 = ADVANCED_FACE('',(#92),#117,.F.);
#92 = FACE_BOUND('',#93,.F.);
#93 = EDGE_LOOP('',(#94,#102,#103,#111));
#94 = ORIENTED_EDGE('',*,*,#95,.F.);
#95 = EDGE_CURVE('',#22,#96,#98,.T.);
#96 = VERTEX_POINT('',#97);
#97 = CARTESIAN_POINT('',(100.,0.,0.));
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(0.,0.,0.));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(1.,0.,-0.));
#102 = ORIENTED_EDGE('',*,*,#21,.T.);
#103 = ORIENTED_EDGE('',*,*,#104,.T.);
#104 = EDGE_CURVE('',#24,#105,#107,.T.);
#105 = VERTEX_POINT('',#106);
#106 = CARTESIAN_POINT('',(100.,0.,10.));
#107 = LINE('',#108,#109);
#108 = CARTESIAN_POINT('',(0.,0.,10.));
#109 = VECTOR('',#110,1.);
#110 = DIRECTION('',(1.,0.,-0.));
#111 = ORIENTED_EDGE('',*,*,#112,.F.);
#112 = EDGE_CURVE('',#96,#105,#113,.T.);
#113 = LINE('',#114,#115);
#114 = CARTESIAN_POINT('',(100.,0.,0.));
#115 = VECTOR('',#116,1.);
#116 = DIRECTION('',(0.,0.,1.));
#117 = PLANE('',#118);
#118 = AXIS2_PLACEMENT_3D('',#119,#120,#121);
#119 = CARTESIAN_POINT('',(0.,0.,0.));
#120 = DIRECTION('',(-0.,1.,0.));
#121 = DIRECTION('',(0.,0.,1.));
#122 = ADVANCED_FACE('',(#123),#141,.T.);
#123 = FACE_BOUND('',#124,.T.);
#124 = EDGE_LOOP('',(#125,#126,#127,#135));
#125 = ORIENTED_EDGE('',*,*,#47,.F.);
#126 = ORIENTED_EDGE('',*,*,#104,.T.);
#127 = ORIENTED_EDGE('',*,*,#128,.T.);
#128 = EDGE_CURVE('',#105,#129,#131,.T.);
#129 = VERTEX_POINT('',#130);
#130 = CARTESIAN_POINT('',(100.,10.,10.));
#131 = LINE('',#132,#133);
#132 = CARTESIAN_POINT('',(100.,0.,10.));
#133 = VECTOR('',#134,1.);
#134 = DIRECTION('',(-0.,1.,0.));
#135 = ORIENTED_EDGE('',*,*,#136,.F.);
#136 = EDGE_CURVE('',#40,#129,#137,.T.);
#137 = LINE('',#138,#139);
#138 = CARTESIAN_POINT('',(0.,10.,10.));
#139 = VECTOR('',#140,1.);
#140 = DIRECTION('',(1.,0.,-0.));
#141 = PLANE('',#142);
#142 = AXIS2_PLACEMENT_3D('',#143,#144,#145);
#143 = CARTESIAN_POINT('',(0.,0.,10.));
#144 = DIRECTION('',(0.,0.,1.));
#145 = DIRECTION('',(1.,0.,-0.));
#146 = ADVANCED_FACE('',(#147),#165,.F.);
#147 = FACE_BOUND('',#148,.F.);
#148 = EDGE_LOOP('',(#149,#150,#151,#159));
#149 = ORIENTED_EDGE('',*,*,#31,.F.);
#150 = ORIENTED_EDGE('',*,*,#95,.T.);
#151 = ORIENTED_EDGE('',*,*,#152,.T.);
#152 = EDGE_CURVE('',#96,#153,#155,.T.);
#153 = VERTEX_POINT('',#154);
#154 = CARTESIAN_POINT('',(100.,10.,0.));
#155 = LINE('',#156,#157);
#156 = CARTESIAN_POINT('',(100.,0.,0.));
#157 = VECTOR('',#158,1.);
#158 = DIRECTION('',(-0.,1.,0.));
#159 = ORIENTED_EDGE('',*,*,#160,.F.);
#160 = EDGE_CURVE('',#32,#153,#161,.T.);
#161 = LINE('',#162,#163);
#162 = CARTESIAN_POINT('',(0.,10.,0.));
#163 = VECTOR('',#164,1.);
#164 = DIRECTION('',(1.,0.,-0.));
#165 = PLANE('',#166);
#166 = AXIS2_PLACEMENT_3D('',#167,#168,#169);
#167 = CARTESIAN_POINT('',(0.,0.,0.));
#168 = DIRECTION('',(0.,0.,1.));
#169 = DIRECTION('',(1.,0.,-0.));
#170 = ADVANCED_FACE('',(#171),#182,.T.);
#171 = FACE_BOUND('',#172,.T.);
#172 = EDGE_LOOP('',(#173,#174,#175,#176));
#173 = ORIENTED_EDGE('',*,*,#160,.F.);
#174 = ORIENTED_EDGE('',*,*,#39,.T.);
#175 = ORIENTED_EDGE('',*,*,#136,.T.);
#176 = ORIENTED_EDGE('',*,*,#177,.F.);
#177 = EDGE_CURVE('',#153,#129,#178,.T.);
#178 = LINE('',#179,#180);
#179 = CARTESIAN_POINT('',(100.,10.,0.));
#180 = VECTOR('',#181,1.);
#181 = DIRECTION('',(0.,0.,1.));
#182 = PLANE('',#183);
#183 = AXIS2_PLACEMENT_3D('',#184,#185,#186);
#184 = CARTESIAN_POINT('',(0.,10.,0.));
#185 = DIRECTION('',(-0.,1.,0.));
#186 = DIRECTION('',(0.,0.,1.));
#187 = ADVANCED_FACE('',(#188),#213,.T.);
#188 = FACE_BOUND('',#189,.T.);
#189 = EDGE_LOOP('',(#190,#191,#199,#207));
#190 = ORIENTED_EDGE('',*,*,#55,.F.);
#191 = ORIENTED_EDGE('',*,*,#192,.T.);
#192 = EDGE_CURVE('',#56,#193,#195,.T.);
#193 = VERTEX_POINT('',#194);
#194 = CARTESIAN_POINT('',(100.,0.5,0.5));
#195 = LINE('',#196,#197);
#196 = CARTESIAN_POINT('',(0.,0.5,0.5));
#197 = VECTOR('',#198,1.);
#198 = DIRECTION('',(1.,0.,-0.));
#199 = ORIENTED_EDGE('',*,*,#200,.T.);
#200 = EDGE_CURVE('',#193,#201,#203,.T.);
#201 = VERTEX_POINT('',#202);
#202 = CARTESIAN_POINT('',(100.,9.5,0.5));
#203 = LINE('',#204,#205);
#204 = CARTESIAN_POINT('',(100.,0.5,0.5));
#205 = VECTOR('',#206,1.);
#206 = DIRECTION('',(-0.,1.,0.));
#207 = ORIENTED_EDGE('',*,*,#208,.F.);
#208 = EDGE_CURVE('',#58,#201,#209,.T.);
#209 = LINE('',#210,#211);
#210 = CARTESIAN_POINT('',(0.,9.5,0.5));
#211 = VECTOR('',#212,1.);
#212 = DIRECTION('',(1.,0.,-0.));
#213 = PLANE('',#214);
#214 = AXIS2_PLACEMENT_3D('',#215,#216,#217);
#215 = CARTESIAN_POINT('',(0.,0.5,0.5));
#216 = DIRECTION('',(0.,0.,1.));
#217 = DIRECTION('',(1.,0.,-0.));
#218 = ADVANCED_FACE('',(#219),#237,.F.);
#219 = FACE_BOUND('',#220,.F.);
#220 = EDGE_LOOP('',(#221,#222,#223,#231));
#221 = ORIENTED_EDGE('',*,*,#208,.F.);
#222 = ORIENTED_EDGE('',*,*,#81,.T.);
#223 = ORIENTED_EDGE('',*,*,#224,.T.);
#224 = EDGE_CURVE('',#74,#225,#227,.T.);
#225 = VERTEX_POINT('',#226);
#226 = CARTESIAN_POINT('',(100.,9.5,9.5));
#227 = LINE('',#228,#229);
#228 = CARTESIAN_POINT('',(0.,9.5,9.5));
#229 = VECTOR('',#230,1.);
#230 = DIRECTION('',(1.,0.,-0.));
#231 = ORIENTED_EDGE('',*,*,#232,.F.);
#232 = EDGE_CURVE('',#201,#225,#233,.T.);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(100.,9.5,0.5));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(0.,0.,1.));
#237 = PLANE('',#238);
#238 = AXIS2_PLACEMENT_3D('',#239,#240,#241);
#239 = CARTESIAN_POINT('',(0.,9.5,0.5));
#240 = DIRECTION('',(-0.,1.,0.));
#241 = DIRECTION('',(0.,0.,1.));
#242 = ADVANCED_FACE('',(#243),#261,.F.);
#243 = FACE_BOUND('',#244,.F.);
#244 = EDGE_LOOP('',(#245,#246,#254,#260));
#245 = ORIENTED_EDGE('',*,*,#73,.F.);
#246 = ORIENTED_EDGE('',*,*,#247,.T.);
#247 = EDGE_CURVE('',#66,#248,#250,.T.);
#248 = VERTEX_POINT('',#249);
#249 = CARTESIAN_POINT('',(100.,0.5,9.5));
#250 = LINE('',#251,#252);
#251 = CARTESIAN_POINT('',(0.,0.5,9.5));
#252 = VECTOR('',#253,1.);
#253 = DIRECTION('',(1.,0.,-0.));
#254 = ORIENTED_EDGE('',*,*,#255,.T.);
#255 = EDGE_CURVE('',#248,#225,#256,.T.);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(100.,0.5,9.5));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(-0.,1.,0.));
#260 = ORIENTED_EDGE('',*,*,#224,.F.);
#261 = PLANE('',#262);
#262 = AXIS2_PLACEMENT_3D('',#263,#264,#265);
#263 = CARTESIAN_POINT('',(0.,0.5,9.5));
#264 = DIRECTION('',(0.,0.,1.));
#265 = DIRECTION('',(1.,0.,-0.));
#266 = ADVANCED_FACE('',(#267),#278,.T.);
#267 = FACE_BOUND('',#268,.T.);
#268 = EDGE_LOOP('',(#269,#270,#271,#272));
#269 = ORIENTED_EDGE('',*,*,#192,.F.);
#270 = ORIENTED_EDGE('',*,*,#65,.T.);
#271 = ORIENTED_EDGE('',*,*,#247,.T.);
#272 = ORIENTED_EDGE('',*,*,#273,.F.);
#273 = EDGE_CURVE('',#193,#248,#274,.T.);
#274 = LINE('',#275,#276);
#275 = CARTESIAN_POINT('',(100.,0.5,0.5));
#276 = VECTOR('',#277,1.);
#277 = DIRECTION('',(0.,0.,1.));
#278 = PLANE('',#279);
#279 = AXIS2_PLACEMENT_3D('',#280,#281,#282);
#280 = CARTESIAN_POINT('',(0.,0.5,0.5));
#281 = DIRECTION('',(-0.,1.,0.));
#282 = DIRECTION('',(0.,0.,1.));
#283 = ADVANCED_FACE('',(#284,#290),#296,.T.);
#284 = FACE_BOUND('',#285,.T.);
#285 = EDGE_LOOP('',(#286,#287,#288,#289));
#286 = ORIENTED_EDGE('',*,*,#112,.F.);
#287 = ORIENTED_EDGE('',*,*,#152,.T.);
#288 = ORIENTED_EDGE('',*,*,#177,.T.);
#289 = ORIENTED_EDGE('',*,*,#128,.F.);
#290 = FACE_BOUND('',#291,.T.);
#291 = EDGE_LOOP('',(#292,#293,#294,#295));
#292 = ORIENTED_EDGE('',*,*,#200,.F.);
#293 = ORIENTED_EDGE('',*,*,#273,.T.);
#294 = ORIENTED_EDGE('',*,*,#255,.T.);
#295 = ORIENTED_EDGE('',*,*,#232,.F.);
#296 = PLANE('',#297);
#297 = AXIS2_PLACEMENT_3D('',#298,#299,#300);
#298 = CARTESIAN_POINT('',(100.,0.,0.));
#299 = DIRECTION('',(1.,0.,-0.));
#300 = DIRECTION('',(0.,0.,1.));
#301 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#305)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#302,#303,#304)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#302 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#303 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#304 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#305 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#302,
  'distance_accuracy_value','confusion accuracy');
#306 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
ENDSEC;
END-ISO-10303-21;
